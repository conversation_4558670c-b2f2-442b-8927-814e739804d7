<?php

use App\Livewire\Dashboard;
use App\Livewire\Auth\Login;
use App\Livewire\Auth\Logout;
use Illuminate\Support\Facades\Route;

// Guest routes (only accessible when not authenticated)
Route::middleware('guest')->group(function () {
    Route::get('/', Login::class)->name('login');
});
Route::get('/logout', Logout::class)->name('logout');

// Protected routes (require authentication)
Route::middleware('auth')->group(function () {
    Route::get('/dashboard', Dashboard::class)->name('dashboard');
});

// Route::get('/', function () {
//     return view('welcome');
// });

// Route::group(['middleware' => 'guest'], function () {
//     //register
//     // Route::livewire('/register', 'auth.register')
//     //     ->layout('layouts.app')->name('auth.register');

//     //login
//     Route::livewire('/', 'auth.login')
//         ->name('auth.login');
// });
