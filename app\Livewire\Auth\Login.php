<?php

namespace App\Livewire\Auth;

use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class Login extends Component
{
    public $username;
    public $password;
    public $remember = false;

    public function login()
    {
        $credentials = $this->validate([
            'username' => 'required',
            'password' => 'required',
        ]);

        $remember = $this->remember == true ? true : false;

        if (Auth::attempt($credentials, $remember)) {
            session()->regenerate();
            return redirect()->intended(route('dashboard'));
        }

        // Add error handling for failed login attempts here
        $this->addError('username', 'Username / Password Salah');
        $this->addError('password', 'Username / Password Salah');
    }

    public function render()
    {
        return view('livewire.auth.login');
    }
}
